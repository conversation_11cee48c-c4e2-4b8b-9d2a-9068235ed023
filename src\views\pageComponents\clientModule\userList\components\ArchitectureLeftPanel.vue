<template>
  <div class="architecture-left-panel">
    <!-- 企业/单位选择器 -->
    <div class="mb-8px">
      <a-tree-select
        v-model:value="memberSelectCompany"
        :tree-data="deptAndCompanyTree"
        :tree-line="true && { showLeafIcon: false }"
        :fieldNames="{ label: 'name', value: 'id', children: 'childs' }"
        class="w-full company-selector-disabled"
        placeholder="选择企业/单位"
        @change="handleCompanyChange"
        disabled
      />
    </div>

    <!-- 部门左侧区域 -->
    <div class="department-left flex-1 flex flex-col">
      <!-- 搜索过滤区 -->
      <div class="bg-#FAFAFB p-12px box-border mb-12px">
        <a-select
          class="w-full"
          v-model:value="filterValue"
          placeholder="搜索部门"
          :options="headerOption"
          show-search
          :filter-option="filterHeaderOption"
          allowClear
          :field-names="{ label: 'name', value: 'id' }"
          @change="handleSelectTreeFilter"
        >
          <template #option="{ name, full_name }">
            <div class="select-option" :title="name">
              <div class="option-name truncate">{{ name }}</div>
              <div class="option-info text-#999 truncate">
                {{ full_name?.replaceAll('/', '>') }}
              </div>
            </div>
          </template>
        </a-select>
      </div>

      <!-- 树形结构区 -->
      <div class="arch-left-list flex-1 relative overflow-hidden">
        <div class="tree-container" :class="{ 'is-loading': spinning }">
          <!-- 自定义加载指示器 -->
          <div v-if="spinning" class="custom-spin-dot">
            <div class="dot-bottom-left"></div>
            <div class="dot-bottom-right"></div>
          </div>

          <a-spin :spinning="false" class="spin-container" size="large">
            <div class="tree-wrapper">
              <a-tree
                ref="treeRef"
                class="absolute w-full"
                :key="treeKey"
                :fieldNames="treeProps"
                v-model:expandedKeys="expandedKeys"
                v-model:selectedKeys="selectedKeys"
                :tree-data="deptTree"
                :auto-expand-parent="false"
                :defaultExpandedKeys="expandedKeys"
                @select="handleSelectDept"
              >
                <template #title="data">
                  <div class="flex w-full items-center justify-between">
                    <a-tooltip :title="data.name" v-if="isEllipsis(data.name)" placement="topLeft">
                      <span class="truncate" :style="{ width: `${120 - (data.level || 0) * 8}px` }">{{ data.name }}</span>
                    </a-tooltip>
                    <span v-else class="truncate" :style="{ width: `${120 - (data.level || 0) * 8}px` }">{{ data.name }}</span>
                    <a-space v-if="true" class="action-icons">
                      <!-- <a-tooltip placement="bottom" v-if="checkPermission('arch_add')">
                    <template #title>
                      <span>添加子部门</span>
                    </template>
                    <PlusOutlined
                      class="text-10px c-#999 mt-8px"
                      @click="handleAddSonDept(data)"
                    />
                  </a-tooltip> -->
                      <a-dropdown>
                        <MoreOutlined class="c-#999 mt-8px" />
                        <template #overlay>
                          <a-menu>
                            <a-menu-item key="1" @click="handleViewDept(data.id)">
                              <div class="gap-8px flex items-center">
                                <span>查看详情</span>
                              </div>
                            </a-menu-item>
                            <a-menu-item key="2" @click="handleEditDept(data.id)" v-if="!isInternalUser && checkPermission('arch_edit')">
                              <div class="gap-8px flex items-center">
                                <span>编辑信息</span>
                              </div>
                            </a-menu-item>
                            <!-- <a-menu-item key="4" @click="handleMoveUp(data.id, 1)" v-if="checkPermission('arch_updown')">
                              <div class="gap-8px flex items-center">
                                <span>上移</span>
                              </div>
                            </a-menu-item> -->
                            <!-- <a-menu-item key="5" @click="handleMoveDown(data.id, 1)" v-if="checkPermission('arch_updown')">
                              <div class="gap-8px flex items-center">
                                <span>下移</span>
                              </div>
                            </a-menu-item> -->
                            <a-menu-item key="6" @click="handleDeleteDept(data.id)" v-if="!isInternalUser && checkPermission('arch_delete') && btnPermission[211008]">
                              <div class="gap-8px flex items-center text-red-500">
                                <span>删除</span>
                              </div>
                            </a-menu-item>
                          </a-menu>
                        </template>
                      </a-dropdown>
                    </a-space>
                  </div>
                </template>
              </a-tree>
            </div>
          </a-spin>
        </div>
      </div>

      <!-- 底部操作区 -->
      <div class="tree-bottom flex mt-12px">
        <div :class="['expand-all-btn flex cursor-pointer items-center justify-center hover:bg-gray-50 transition-colors', isInternalUser ? 'flex-1' : 'flex-1']" @click="toggleExpandAll">
          <component :is="DoubleRightOutlined" :class="['expand-icon', isAllExpanded ? 'rotate-up' : 'rotate-down']" />
          <span class="expand-text">{{ isAllExpanded ? '收起全部' : '展开全部' }}</span>
        </div>
        <div
          class="add-dept-btn flex flex-1 cursor-pointer items-center justify-center text-white hover:bg-blue-600 transition-colors"
          @click="handleAddDept"
          v-if="!isInternalUser && checkPermission('arch_add')"
        >
          <PlusCircleOutlined class="mr-4px" />
          <span class="add-text">新建部门</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, onMounted, nextTick } from 'vue'
import { message, Modal } from 'ant-design-vue'
import { MoreOutlined, DoubleRightOutlined } from '@ant-design/icons-vue'
import { GetCompanyTreeAuto, type TreeNode, type SearchOption, DeleteDepartment } from '@/servers/CompanyArchitecture'
import { checkPagePermission } from '@/utils'
import { usePermission } from '@/hook/usePermission'

const { btnPermission } = usePermission()
// 简单的深拷贝函数，避免依赖lodash
const cloneDeep = <T,>(obj: T): T => {
  if (obj === null || typeof obj !== 'object') return obj
  if (obj instanceof Date) return new Date(obj.getTime()) as unknown as T
  if (obj instanceof Array) return obj.map((item) => cloneDeep(item)) as unknown as T
  if (typeof obj === 'object') {
    const clonedObj = {} as T
    for (const key in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, key)) {
        clonedObj[key] = cloneDeep(obj[key])
      }
    }
    return clonedObj
  }
  return obj
}

// 获取当前用户信息，判断是否为内部用户
const getCurrentUserScope = () => {
  try {
    const userDataStr = localStorage.getItem('userData')
    if (userDataStr) {
      const userData = JSON.parse(userDataStr)
      return userData.scope || 2 // 默认为外部用户
    }
    return 2 // 默认为外部用户
  } catch (error) {
    console.error('获取用户scope失败:', error)
    return 2 // 默认为外部用户
  }
}

// 判断是否为内部用户（scope=1为内部用户，scope=2为外部用户）
const isInternalUser = computed(() => getCurrentUserScope() === 1)

// Props
interface Props {
  // 当前选中的部门/企业ID
  selectedId?: string
  // 权限检查函数
  // eslint-disable-next-line no-unused-vars
  checkPermission?: (_permission: string) => boolean
  // 默认展开的层级数（1表示只展开第一层，2表示展开前两层，以此类推）
  defaultExpandLevels?: number
}

// 修正默认值，确保类型匹配
const props = withDefaults(defineProps<Props>(), {
  selectedId: '',
  // 默认实现：忽略未使用的参数（下划线表示有意未使用）
  checkPermission: () => () => true,
  // 默认展开前两层
  defaultExpandLevels: 2,
})

// 新建部门
const handleAddDept = () => {
  emit('add-dept')
}
// Emits
const emit = defineEmits<{
  // 选择部门/企业变化
  'select-change': [id: string, data: TreeNode | null]
  // 添加部门
  'add-dept': [parentId?: string]
  // 编辑部门
  'edit-dept': [id: string]
  // 查看部门详情
  'view-dept': [id: string, nodeType: number]
  // 部门操作成功（删除、移动等）
  'dept-operation-success': []
}>()

// 响应式数据
const memberSelectCompany = ref<string>('')
const filterValue = ref<any>(null)
const expandedKeys = ref<string[]>([])
const selectedKeys = ref<string[]>([])
const isAllExpanded = ref(true)
const spinning = ref(false)
const treeRef = ref()
const treeKey = ref(0)
// 标记是否是第一次加载
const isFirstLoad = ref(true)

// 数据源
const deptAndCompanyTree = ref<TreeNode[]>([])
const enterpriseAccountOption = ref<TreeNode[]>([])
const deptTree = ref<TreeNode[]>([])
const headerOption = ref<SearchOption[]>([])

// 树形结构字段映射
const treeProps = ref({
  children: 'childs',
  title: 'name',
  key: 'id',
})

// 监听展开状态变化
watch(
  expandedKeys,
  (newVal) => {
    // 获取所有可展开的节点（有子节点的节点）
    const allExpandableKeys = getAllExpandableKeys(deptTree.value)

    // 确保比较时类型一致，都转换为字符串
    const normalizedExpandedKeys = newVal.map((key) => String(key))
    const normalizedExpandableKeys = allExpandableKeys.map((key) => String(key))

    // 简化判断逻辑：只有当所有可展开的节点都在展开列表中时，才认为是全部展开
    if (normalizedExpandableKeys.length === 0) {
      isAllExpanded.value = false
    } else {
      isAllExpanded.value = normalizedExpandableKeys.every((key) => normalizedExpandedKeys.includes(key))
    }

    console.log('展开状态更新:', {
      expandedCount: normalizedExpandedKeys.length,
      expandableCount: normalizedExpandableKeys.length,
      isAllExpanded: isAllExpanded.value,
    })
  },
  { deep: true },
)

// 监听外部选中状态变化
watch(
  () => props.selectedId,
  (newVal) => {
    if (newVal && newVal !== selectedKeys.value[0]) {
      selectedKeys.value = [newVal]

      // 查找对应的节点信息以应用样式
      const findNodeForStyle = (nodes: TreeNode[]): TreeNode | null => {
        for (const node of nodes) {
          if (String(node.id) === String(newVal)) {
            return node
          }
          if (node.childs?.length) {
            const result = findNodeForStyle(node.childs)
            if (result) return result
          }
        }
        return null
      }

      // 延迟应用样式，确保DOM已更新
      nextTick(() => {
        const node = findNodeForStyle(deptTree.value) || findNodeForStyle(enterpriseAccountOption.value)
        if (node) {
          forceApplySelectedStyle(newVal, node.name)
        }
      })
    }
  },
  { immediate: true },
)

// 生命周期
onMounted(async () => {
  // 检查当前用户是否有权限访问用户管理页面
  const hasUserManagementPermission = checkPagePermission('/userLists')

  if (hasUserManagementPermission) {
    await getAllDept('init')
    // changeDeptTree() 会在 getAllDept 中自动调用，不需要在这里重复调用
  } else {
    console.warn('用户无权限访问用户管理页面，跳过组织架构数据加载')
  }
})

const handleDeleteDept = async (id: string) => {
  try {
    // 二次确认弹窗（使用 Modal.confirm）
    Modal.confirm({
      title: '确认删除',
      content: '是否确认删除部门？删除前，请先移除部门内所有人员再进行操作，否则会操作失败.',
      okText: '确认删除',
      cancelText: '取消',
      okType: 'danger', // 确认按钮为危险色
      // 点击确认按钮的回调
      onOk: async () => {
        spinning.value = true // 显示加载状态
        try {
          // 调用删除接口
          await DeleteDepartment({
            id,
            p_id: '',
            company_id: memberSelectCompany.value,
            name: '',
            header_ids: 0,
            oa_id: '',
          })
          message.success('删除成功')
          await getAllDept() // 刷新部门树
          emit('dept-operation-success') // 通知父组件
        } catch (error: any) {
          const errorMessage = error?.message || '删除失败'
          message.error(errorMessage)
        } finally {
          spinning.value = false // 关闭加载状态
        }
      },
      // 点击取消按钮的回调（可选）
      onCancel: () => {
        // 无需操作，弹窗会自动关闭
      },
    })
  } catch (error) {
    console.error('弹窗操作异常:', error)
  }
}
// 方法定义
// 获取所有可展开的节点（有子节点的节点）
const getAllExpandableKeys = (data: TreeNode[]): string[] => {
  const keys: string[] = []
  const traverse = (nodes: TreeNode[]) => {
    nodes.forEach((node) => {
      // 只有有子节点的节点才是可展开的
      if (node.childs?.length && node.childs.length > 0) {
        keys.push(String(node.id))
        traverse(node.childs)
      }
    })
  }
  traverse(data)
  return keys
}

const getAllDept = async (type?: string) => {
  try {
    const res = await GetCompanyTreeAuto()
    const data = res.data || []

    // 数据格式转换和标准化
    const normalizeData = (nodes: any[]): TreeNode[] => {
      return nodes.map((node) => {
        // 处理 type 字段：接口返回数字类型，需要转换为字符串格式
        let nodeTypeString: '企业=1' | '单位=2' | '部门=3'
        switch (node.type) {
          case 1:
            nodeTypeString = '企业=1'
            break
          case 2:
            nodeTypeString = '单位=2'
            break
          case 3:
            nodeTypeString = '部门=3'
            break
          default:
            nodeTypeString = '部门=3'
        }

        // 处理 parent_type 字段
        let parentTypeString: '企业=1' | '单位=2' | '部门=3'
        switch (node.parent_type) {
          case 1:
            parentTypeString = '企业=1'
            break
          case 2:
            parentTypeString = '单位=2'
            break
          case 3:
            parentTypeString = '部门=3'
            break
          default:
            parentTypeString = '企业=1'
        }

        const normalizedNode: TreeNode = {
          id_with_type: node.id_with_type || '',
          p_id_with_type: node.p_id_with_type || '',
          id: node.id,
          p_id: node.p_id || 0,
          parent_type: parentTypeString,
          name: node.name || '',
          full_name: node.full_name || '',
          type: nodeTypeString,
          order: node.order || 0,
          oa_id: node.oa_id || 0,
          childs: node.childs && node.childs.length > 0 ? normalizeData(node.childs) : [],

          // 兼容性字段（保留旧字段名以防旧代码使用）
          department_name: node.name || node.department_name || '',
          company_name: node.full_name || node.company_name || '',
        }

        // 保留原始数字类型以便兼容
        ;(normalizedNode as any).originalType = node.type

        return normalizedNode
      })
    }

    const normalizedData = normalizeData(data)

    // 设置层级信息
    const setLevel = (nodes: TreeNode[], level = 1, parentPath = '') => {
      nodes.forEach((node) => {
        node.level = level
        node.class = `level-${level}`
        if (node.childs?.length) {
          setLevel(node.childs, level + 1, `${parentPath}/${node.name}`)
        }
      })
    }

    setLevel(normalizedData)
    enterpriseAccountOption.value = cloneDeep(normalizedData)

    // 过滤企业节点
    const filterNodes = (nodes: TreeNode[]): TreeNode[] => {
      return nodes
        .filter((node) => node.type === '企业=1')
        .map((node) => ({
          ...node,
          childs: node.childs ? filterNodes(node.childs) : [],
        }))
    }

    deptAndCompanyTree.value = filterNodes(normalizedData)

    // 初始化时自动选择第一个企业
    if (type === 'init' && deptAndCompanyTree.value.length > 0) {
      memberSelectCompany.value = String(deptAndCompanyTree.value[0].id)

      // 自动选择企业后，立即更新部门树
      changeDeptTree()
    }

    findHeaderOption()
  } catch (error) {
    console.error('获取组织架构数据失败:', error)
    message.error('获取组织架构数据失败')
  }
}

const changeDeptTree = () => {
  if (!memberSelectCompany.value) {
    deptTree.value = []
    return
  }

  const findCompanyTree = (data: TreeNode[], companyId: string): TreeNode[] => {
    for (const item of data) {
      if (String(item.id) === companyId) {
        return [item]
      }
      if (item.childs?.length) {
        const result = findCompanyTree(item.childs, companyId)
        if (result.length > 0) return result
      }
    }
    return []
  }

  deptTree.value = findCompanyTree(enterpriseAccountOption.value, memberSelectCompany.value)

  console.log('🌲 部门树数据:', deptTree.value, '是否第一次加载:', isFirstLoad.value)

  // 只在第一次加载时自动展开前两层
  if (isFirstLoad.value) {
    setTimeout(() => {
      const maxExpandLevel = props.defaultExpandLevels || 2
      const keys: string[] = []

      console.log(`🚀 第一次加载，自动展开前${maxExpandLevel}层`)

      const collectExpandKeys = (nodes: TreeNode[], level = 1) => {
        nodes.forEach((node) => {
          console.log(`检查节点: ${node.name}, 层级: ${level}, 子节点: ${node.childs?.length || 0}`)

          // 如果有子节点且在展开层级范围内，添加到展开列表
          if (node.childs && node.childs.length > 0 && level <= maxExpandLevel) {
            keys.push(String(node.id))
            console.log(`✅ 添加展开节点: ${node.name} (ID: ${node.id})`)

            // 继续处理子节点
            if (level < maxExpandLevel) {
              collectExpandKeys(node.childs, level + 1)
            }
          }
        })
      }

      collectExpandKeys(deptTree.value)

      console.log(`🎯 第一次加载收集到的展开节点:`, keys)

      // 设置展开状态 - 使用新数组确保响应式更新
      expandedKeys.value = [...keys]

      // 使用 nextTick 确保状态更新后再强制刷新组件
      nextTick(() => {
        // 强制更新树组件以确保展开状态生效
        treeKey.value++

        console.log(`🔄 第一次加载设置后的展开状态:`, expandedKeys.value, '树组件key:', treeKey.value)

        // 再次确认展开状态
        setTimeout(() => {
          console.log('🔍 延迟检查展开状态:', {
            expandedKeysLength: expandedKeys.value.length,
            expandedKeys: expandedKeys.value.slice(0, 5),
            treeKey: treeKey.value,
          })

          // 检查展开状态是否生效
          if (expandedKeys.value.length > 0) {
            console.log('✅ 展开状态已设置，无需重试')
          } else {
            console.log('⚠️ 展开状态设置失败，但不进行重试以避免闪烁')
          }
        }, 1000)
      })

      // 标记已经不是第一次加载了
      isFirstLoad.value = false
    }, 200)
  } else {
    console.log('⏭️ 非第一次加载，跳过自动展开')
  }

  // 延迟设置选中状态，确保Tree组件已经渲染完成
  setTimeout(() => {
    if (deptTree.value.length > 0 && deptTree.value[0]) {
      const enterpriseNode = deptTree.value[0]
      const enterpriseNodeId = String(enterpriseNode.id)

      // 确保选择的是企业节点（第一级）
      const originalType = (enterpriseNode as any).originalType || enterpriseNode.type
      if (originalType === 1 || enterpriseNode.type === '企业=1') {
        // 直接设置选中状态，不要先清空
        selectedKeys.value = [enterpriseNodeId]

        // 使用多重 nextTick 确保 DOM 完全更新
        nextTick(() => {
          nextTick(() => {
            // 通过DOM操作强制添加选中样式，确保样式正确应用
            setTimeout(() => {
              // 先移除所有节点的强制选中样式
              document.querySelectorAll('.force-selected').forEach((el) => {
                el.classList.remove('force-selected')
              })

              // 通过节点ID查找树节点（更精确的查找方式）
              const treeElement = document.querySelector(`[data-node-key="${enterpriseNodeId}"]`)

              if (treeElement) {
                // 添加选中样式类
                treeElement.classList.add('ant-tree-treenode-selected', 'force-selected')
                const contentWrapper = treeElement.querySelector('.ant-tree-node-content-wrapper')
                if (contentWrapper) {
                  contentWrapper.classList.add('ant-tree-node-content-wrapper-selected')
                }
                console.log('✅ 成功应用选中样式到节点:', enterpriseNode.name)
              } else {
                // 如果通过data-node-key找不到，尝试通过企业名称查找
                const allTreeNodes = document.querySelectorAll('.ant-tree-treenode')
                let foundElement: Element | null = null

                allTreeNodes.forEach((node) => {
                  const title = node.querySelector('.ant-tree-title')?.textContent
                  if (title && title.includes(enterpriseNode.name)) {
                    foundElement = node
                  }
                })

                if (foundElement) {
                  ;(foundElement as Element).classList.add('ant-tree-treenode-selected', 'force-selected')
                  const contentWrapper = (foundElement as Element).querySelector('.ant-tree-node-content-wrapper')
                  if (contentWrapper) {
                    contentWrapper.classList.add('ant-tree-node-content-wrapper-selected')
                  }
                  console.log('✅ 通过名称匹配成功应用选中样式到节点:', enterpriseNode.name)
                } else {
                  console.warn('⚠️ 无法找到对应的树节点元素:', enterpriseNode.name)
                }
              }
            }, 200)

            // 触发选择变化事件
            emit('select-change', enterpriseNodeId, enterpriseNode)
          })
        })
      }
    }
  }, 150)
}

const findHeaderOption = () => {
  const options: SearchOption[] = []

  const traverse = (nodes: TreeNode[], parentPath = '') => {
    nodes.forEach((node) => {
      if (node.type !== '企业=1') {
        // 将字符串类型转换为数字类型以匹配 SearchOption 接口
        let typeNumber: number
        const nodeType = node.type as '企业=1' | '单位=2' | '部门=3'
        switch (nodeType) {
          case '企业=1':
            typeNumber = 1
            break
          case '单位=2':
            typeNumber = 2
            break
          case '部门=3':
            typeNumber = 3
            break
          default:
            // 如果是数字类型，直接使用原始值
            typeNumber = (node as any).originalType || 3
        } // 不包括企业节点
        options.push({
          id: String(node.id),
          name: node.name,
          full_name: parentPath ? `${parentPath}/${node.name}` : node.name,
          type: typeNumber,
        })
      }

      if (node.childs?.length) {
        const currentPath = parentPath ? `${parentPath}/${node.name}` : node.name
        traverse(node.childs, currentPath)
      }
    })
  }

  traverse(enterpriseAccountOption.value)
  headerOption.value = options
}

// 事件处理方法
const handleCompanyChange = () => {
  selectedKeys.value = []
  filterValue.value = null

  // 切换企业时，不是第一次加载，所以不会自动展开
  console.log('🔄 切换企业，isFirstLoad:', isFirstLoad.value)

  changeDeptTree()
  findHeaderOption()

  // 注意：changeDeptTree() 中已经会自动选择第一个节点并触发 select-change 事件
  // 所以这里不需要再次触发事件
}

// 强制应用选中样式的辅助方法
const forceApplySelectedStyle = (nodeId: string, nodeName: string) => {
  setTimeout(() => {
    // 先移除所有节点的强制选中样式
    document.querySelectorAll('.force-selected').forEach((el) => {
      el.classList.remove('force-selected', 'ant-tree-treenode-selected')
      const wrapper = el.querySelector('.ant-tree-node-content-wrapper')
      if (wrapper) {
        wrapper.classList.remove('ant-tree-node-content-wrapper-selected')
      }
    })

    // 通过节点ID查找树节点
    let treeElement = document.querySelector(`[data-node-key="${nodeId}"]`)

    if (!treeElement) {
      // 如果通过data-node-key找不到，尝试通过节点名称查找
      const allTreeNodes = document.querySelectorAll('.ant-tree-treenode')
      allTreeNodes.forEach((node) => {
        const title = node.querySelector('.ant-tree-title')?.textContent
        if (title && title.includes(nodeName)) {
          treeElement = node
        }
      })
    }

    if (treeElement) {
      // 添加选中样式类
      treeElement.classList.add('ant-tree-treenode-selected', 'force-selected')
      const contentWrapper = treeElement.querySelector('.ant-tree-node-content-wrapper')
      if (contentWrapper) {
        contentWrapper.classList.add('ant-tree-node-content-wrapper-selected')
      }
      console.log('✅ 成功应用选中样式到节点:', nodeName, 'ID:', nodeId)
    } else {
      console.warn('⚠️ 无法找到对应的树节点元素:', nodeName, 'ID:', nodeId)
    }
  }, 100)
}

const handleSelectDept = (selectedKeysValue: string[]) => {
  if (selectedKeysValue.length > 0) {
    const selectedId = selectedKeysValue[0]

    const findNode = (nodes: TreeNode[]): TreeNode | null => {
      for (const node of nodes) {
        // 尝试多种比较方式确保匹配
        if (String(node.id) === String(selectedId) || Number(node.id) === Number(selectedId)) {
          console.log('✅ 找到匹配节点:', node.name, 'ID:', node.id)
          return node
        }

        if (node.childs?.length) {
          const result = findNode(node.childs)
          if (result) return result
        }
      }
      return null
    }

    const selectedNode = findNode(deptTree.value)

    // 如果在deptTree中找不到，尝试在enterpriseAccountOption中查找
    if (!selectedNode) {
      const selectedNodeInEnterprise = findNode(enterpriseAccountOption.value)
      if (selectedNodeInEnterprise) {
        // 强制应用选中样式
        forceApplySelectedStyle(selectedId, selectedNodeInEnterprise.name)
        emit('select-change', selectedId, selectedNodeInEnterprise)
        return
      }
    }

    if (selectedNode) {
      // 强制应用选中样式
      forceApplySelectedStyle(selectedId, selectedNode.name)
    }

    emit('select-change', selectedId, selectedNode)
  } else {
    console.log('⚠️ selectedKeysValue 为空，不处理选择')
  }
}

const handleSelectTreeFilter = (newVal: string) => {
  if (!newVal) {
    selectedKeys.value = []
    return
  }

  // 定位到选中节点并展开路径
  const findAndExpandPath = (nodes: TreeNode[], targetId: string, path: string[] = []): boolean => {
    for (const node of nodes) {
      const currentPath = [...path, String(node.id)]

      if (String(node.id) === targetId) {
        expandedKeys.value = [...new Set([...expandedKeys.value, ...path])]
        selectedKeys.value = [targetId]

        // 触发选择事件
        emit('select-change', targetId, node)
        return true
      }

      if (node.childs?.length) {
        if (findAndExpandPath(node.childs, targetId, currentPath)) {
          return true
        }
      }
    }
    return false
  }

  findAndExpandPath(deptTree.value, newVal)
}

const filterHeaderOption = (input: string, option: any) => {
  return option.name.toLowerCase().includes(input.toLowerCase())
}

const toggleExpandAll = () => {
  console.log('🔄 toggleExpandAll 点击:', {
    currentState: isAllExpanded.value,
    action: isAllExpanded.value ? '收起全部' : '展开全部',
    currentExpandedKeys: expandedKeys.value.length,
    currentExpandedKeysArray: expandedKeys.value,
    treeData: deptTree.value.length,
  })

  spinning.value = true

  if (isAllExpanded.value) {
    // 收起全部 - 使用最简单直接的方法
    console.log('🔽 执行收起全部，清空展开状态')

    // 直接设置为空数组
    expandedKeys.value = []

    console.log('🔽 收起全部执行完成，当前状态:', expandedKeys.value)
  } else {
    // 展开全部 - 展开所有有子节点的节点
    const expandableKeys = getAllExpandableKeys(deptTree.value).map((key) => String(key))
    expandedKeys.value = expandableKeys

    console.log('🔼 执行展开全部:', {
      expandableCount: expandableKeys.length,
      expandedKeys: expandedKeys.value,
      sampleKeys: expandableKeys.slice(0, 10),
    })
  }

  // 强制重新渲染Tree组件
  treeKey.value++

  console.log('🔄 强制更新树组件，新的 treeKey:', treeKey.value)

  setTimeout(() => {
    spinning.value = false
    console.log('🔄 操作完成，最终状态:', {
      expandedKeys: expandedKeys.value,
      expandedKeysLength: expandedKeys.value.length,
      isAllExpanded: isAllExpanded.value,
    })
  }, 300)
}

// 操作方法
const handleViewDept = (id: string) => {
  console.log('查看部门详情:', id)

  // 查找当前节点信息以获取类型
  const findNode = (nodes: TreeNode[]): TreeNode | null => {
    for (const node of nodes) {
      if (String(node.id) === String(id)) {
        return node
      }
      if (node.childs?.length) {
        const result = findNode(node.childs)
        if (result) return result
      }
    }
    return null
  }

  const currentNode = findNode(deptTree.value) || findNode(enterpriseAccountOption.value)

  // 根据节点类型确定type参数：企业传1，部门传3
  let nodeType = 3 // 默认为部门
  if (currentNode) {
    const originalType = (currentNode as any).originalType || currentNode.type
    if (originalType === 1 || currentNode.type === '企业=1') {
      nodeType = 1 // 企业
    } else if (originalType === 3 || currentNode.type === '部门=3') {
      nodeType = 3 // 部门
    }
  }

  emit('view-dept', id, nodeType)
}
const handleEditDept = (id: string) => {
  emit('edit-dept', id)
}
// 检测文本是否需要省略号
const isEllipsis = (text: string) => {
  // 创建一个隐藏的 span 元素用于测量文本宽度
  const span = document.createElement('span')
  span.style.visibility = 'hidden'
  span.style.position = 'fixed'
  span.style.whiteSpace = 'nowrap'
  span.style.fontSize = '14px'
  span.style.fontFamily = 'inherit'
  span.innerText = text
  document.body.appendChild(span)
  const width = span.offsetWidth
  document.body.removeChild(span)
  // 120px 是设置的最大宽度
  return width > 120
}

// 暴露方法给父组件
defineExpose({
  refresh: getAllDept,
  getSelectedCompany: () => memberSelectCompany.value,
  getSelectedDept: () => selectedKeys.value[0] || '',
})
</script>

<style lang="scss" scoped>
.architecture-left-panel {
  position: relative;
  z-index: 1;
  display: flex !important;
  flex-direction: column !important;
  flex-grow: 0 !important; /* 防止扩展 */
  flex-shrink: 0 !important; /* 防止被压缩 */
  width: 230px !important;
  min-width: 230px !important;
  max-width: 230px !important;
  height: 100%;
  margin-right: 16px;
}

.department-left {
  background: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgb(0 0 0 / 6%);
  transition: box-shadow 0.2s;

  &:hover {
    box-shadow: 0 4px 12px rgb(0 0 0 / 10%);
  }
}

.arch-left-list {
  flex: 1;
  min-height: 300px;
  max-height: calc(100vh - 200px);
  overflow: visible auto !important;
  border-top: 1px solid #f0f0f0;

  :deep(.ant-tree-treenode) {
    display: flex;
    align-items: center;
    width: 204px;
    height: 32px;
    color: #666;

    &:hover {
      color: #409eff;
      background-color: #eef2fa;

      .action-icons {
        opacity: 1;
      }
    }

    .ant-tree-node-content-wrapper {
      display: inline-block;
      flex: 1;

      &:hover {
        background-color: #eef2fa;
      }
    }

    .ant-tree-switcher {
      &.ant-tree-switcher_close {
        // 对于前两层节点，强制显示为展开状态
        .anticon-caret-right {
          transform: rotate(90deg) !important;
        }
      }
    }

    &.force-selected {
      color: #409eff !important;
      background-color: #eef2fa !important;

      .ant-tree-node-content-wrapper {
        color: #409eff !important;
        background-color: #eef2fa !important;
      }
    }
  }

  // 选中状态样式 - 使用更高的优先级确保样式生效
  :deep(.ant-tree-treenode-selected) {
    color: #409eff !important;
    background-color: #eef2fa !important;

    .ant-tree-node-content-wrapper {
      color: #409eff !important;
      background-color: #eef2fa !important;
    }
  }

  :deep(.ant-tree-node-selected) {
    background-color: #eef2fa !important;
  }

  // 选中节点的内容包装器样式
  :deep(.ant-tree-node-content-wrapper-selected) {
    color: #409eff !important;
    background-color: #eef2fa !important;
  }

  // 强制为选中的节点添加样式（通过动态类名）
  :deep(.force-selected) {
    color: #409eff !important;
    background-color: #eef2fa !important;

    .ant-tree-node-content-wrapper {
      color: #409eff !important;
      background-color: #eef2fa !important;
    }

    &.ant-tree-treenode {
      color: #409eff !important;
      background-color: #eef2fa !important;
    }
  }

  // 确保选中状态在hover时也保持正确的样式
  :deep(.ant-tree-treenode-selected:hover) {
    color: #409eff !important;
    background-color: #eef2fa !important;

    .ant-tree-node-content-wrapper {
      color: #409eff !important;
      background-color: #eef2fa !important;
    }
  }

  :deep(.anticon-caret-down) {
    color: #c0c0c0;
  }

  :deep(.action-icons) {
    opacity: 0;
    transition: opacity 0.2s;
  }

  // 树形容器样式
  .tree-container {
    position: relative;
    height: 100%;
    min-height: 300px;

    &.is-loading {
      &::before {
        position: absolute;
        inset: 0;
        z-index: 999;
        content: '';
        background: rgb(255 255 255 / 80%);
      }
    }

    .spin-container {
      height: 100%;

      .tree-wrapper {
        position: relative;
        height: 100%;
      }
    }
  }

  // 自定义Ant Design风格的加载指示器
  .custom-spin-dot {
    position: absolute;
    top: 50%;
    left: 50%;
    z-index: 1000;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    animation: antSpinRotate 1.2s infinite linear;

    &::before,
    &::after,
    .dot-bottom-left,
    .dot-bottom-right {
      position: absolute;
      width: 6px;
      height: 6px;
      content: '';
      background: #1890ff;
      border-radius: 50%;
      animation: antSpinDotSpin 1s infinite linear alternate;
    }

    &::before {
      top: 0;
      left: 0;
      animation-delay: 0s;
    }

    &::after {
      top: 0;
      right: 0;
      animation-delay: 0.4s;
    }

    .dot-bottom-left {
      bottom: 0;
      left: 0;
      animation-delay: 0.8s;
    }

    .dot-bottom-right {
      right: 0;
      bottom: 0;
      animation-delay: 1.2s;
    }
  }

  // Ant Design风格的动画
  @keyframes antSpinRotate {
    to {
      transform: rotate(360deg);
    }
  }

  @keyframes antSpinDotSpin {
    0% {
      opacity: 0.3;
      transform: scale(0.75);
    }

    50% {
      opacity: 1;
      transform: scale(1);
    }

    100% {
      opacity: 0.3;
      transform: scale(0.75);
    }
  }

  // 完全隐藏原始的ant-spin指示器
  :deep(.tree-container) {
    .ant-spin {
      .ant-spin-dot {
        display: none !important;
      }

      .ant-spin-text {
        display: none !important;
      }
    }

    // 确保ant-spin容器不影响布局
    .ant-spin-container {
      position: static !important;
    }
  }
}

// 移除复杂的节点样式，使用简洁的参考样式

.tree-bottom {
  overflow: hidden;
  border-top: 1px solid #f0f0f0;
  border-radius: 0 0 6px 6px;

  .expand-all-btn {
    padding: 8px;
    background: #fff;
    border-right: 1px solid #f0f0f0;
    transition: all 0.2s;

    .expand-icon {
      margin-right: 4px;
      font-size: 14px;
      color: #666;
      transition: transform 0.2s;
    }

    .expand-text {
      font-size: 12px;
      font-weight: 500;
      color: #262626;
    }

    &:hover {
      background-color: #f5f5f5;

      .expand-icon {
        color: #1890ff;
      }

      .expand-text {
        color: #1890ff;
      }
    }
  }

  .add-dept-btn {
    padding: 8px;
    background: #1890ff;
    transition: all 0.2s;

    .add-text {
      font-size: 14px;
      font-weight: 500;
      color: #fff;
    }

    &:hover {
      background-color: #40a9ff;
    }
  }
}

.rotate-down {
  transform: rotate(90deg);
}

.rotate-up {
  transform: rotate(-90deg);
}

.select-option {
  padding: 8px 12px;
  border-radius: 4px;
  transition: background-color 0.2s;

  &:hover {
    background-color: #f5f5f5;
  }

  .option-name {
    margin-bottom: 2px;
    font-weight: 500;
    color: #262626;
  }

  .option-info {
    font-size: 12px;
    color: #8c8c8c;
  }
}

// 企业选择器样式优化
:deep(.ant-tree-select) {
  .ant-select-selector {
    border-color: #d9d9d9;
    border-radius: 6px;
    transition: all 0.2s;

    &:hover {
      border-color: #40a9ff;
    }

    &.ant-select-focused {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgb(24 144 255 / 20%);
    }
  }
}

// 搜索框样式优化
:deep(.ant-select) {
  .ant-select-selector {
    border-color: #d9d9d9;
    border-radius: 6px;
    transition: all 0.2s;

    &:hover {
      border-color: #40a9ff;
    }

    &.ant-select-focused {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgb(24 144 255 / 20%);
    }
  }
}
</style>
